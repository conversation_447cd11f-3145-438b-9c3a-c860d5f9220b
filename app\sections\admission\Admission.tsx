"use client";
import Image from "next/image";
import Link from 'next/link'; // Import Link

const Admission = () => {
  return (
    <section
      className="pt-24 pb-12 relative overflow-hidden"
      id="admission2025-2026"
    >
      {/* Top Content */}
      <div className="container max-w-7xl mx-auto space-y-6 relative z-20 text-center px-4">
        <h2 className="text-custom-green">Admission 2025 - 2026</h2>
        <p className="text-custom-new-green text-base md:text-lg">
          This admission notice from Jamal <PERSON> highlights the online
          application process for the academic year 2025-2026. It provides
          essential instructions for applicants, including required documents.
          The college emphasizes transparency, stating no capitation fees or
          donations are collected for admission.
        </p>
        <div className="flex justify-center">
          {/* Replace <a> with Link */}
          <Link
            href="https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/PDFs/Admission/Admission2025-2026/Admission-2025-2026.pdf"
            target="_blank" // Open in new tab
            rel="noopener noreferrer" // Security measure for target="_blank"
            className="font-poppins bg-custom-green hover:bg-green-800 text-white font-semibold px-6 md:px-8 py-4 md:py-6 text-lg md:text-xl rounded-2xl shadow-[0px_0px_34px_0px_#00000026] flex items-center gap-4 md:gap-8 lg:gap-36 transition"
          >
            <span>Download</span>
            <Image
              src="/courses/download_icon.svg" // Ensure the path starts with / if it's in public
              alt="Download Icon"
              width={24}
              height={24}
            />
          </Link>
        </div>
      </div>

      {/* Wavy Divider */}
      <div className="w-full mt-16 mb-16 relative z-10">
        <Image
          src="/Vector (Stroke).svg"
          alt="Wavy Divider"
          width={0}
          height={0}
          className="w-full h-auto"
        />
      </div>

      {/* New Programmes Section */}
      <div className="container max-w-7xl mx-auto space-y-8 relative z-20 px-4 text-custom-green">
        <h3 className="text-custom-green text-2xl md:text-4xl font-medium underline">
          New Programmes
        </h3>
        <ul className="space-y-8 text-lg md:text-xl font-poppins font-medium pl-4 md:pl-6">
          <li className="flex items-start">
            <span className="text-custom-green mr-3">•</span> {/* Added margin-right for spacing */}
            {/* Replaced <a> with <span> and removed link styles */}
            <span className="transition">
              B.Sc. Artificial Intelligence & Machine Learning [Self Finance –
              Men] *
            </span>
          </li>
          <li className="flex items-start">
          <span className="text-custom-green mr-3">•</span> {/* Added margin-right for spacing */}
            <span className="text-custom-green ml-2"> B.Com. Computer Applications [Self Finance - Women] *</span>
 
          </li>

        </ul>
      </div>
    </section>
  );
};

export default Admission;
