import React from 'react';
import Image from 'next/image';

interface PlacementFormModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const PlacementFormModal: React.FC<PlacementFormModalProps> = ({ isOpen, onClose }) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4">
      <div className="bg-white rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="sticky top-0 bg-white p-4 border-b border-gray-200 flex justify-between items-center">
          <h3 className="text-2xl font-bold text-custom-green">Student Career Enrollment Form</h3>
          <button 
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
            aria-label="Close"
          >
            <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        
        {/* Content */}
        <div className="p-6">
          <div className="mb-6 text-center">
            <Image
              src="/placement/form-qr.png"
              alt="Scan QR Code"
              width={200}
              height={200}
              className="mx-auto mb-4"
            />
            <p className="text-lg text-gray-700 mb-4">
              Scan the QR code or click the button below to access the Student Career Enrollment Form
            </p>
            <a
              href="https://forms.gle/9iFcC7m3v9XmZ4vD7"
              target="_blank"
              rel="noopener noreferrer"
              className="inline-block bg-custom-green text-white px-6 py-3 rounded-lg font-medium hover:bg-green-700 transition-colors duration-300"
            >
              Open Enrollment Form
            </a>
          </div>
          
          <div className="mt-8 border-t border-gray-200 pt-6">
            <h4 className="text-lg font-semibold text-custom-green mb-4">Instructions:</h4>
            <ul className="space-y-2 text-gray-700">
              <li>• Fill out all the required fields in the form</li>
              <li>• Double-check your information before submitting</li>
              <li>• You will receive a confirmation email after successful submission</li>
              <li>• For any issues, please contact the placement cell</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PlacementFormModal;
