import React, { useState } from 'react';
import Image from 'next/image';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface PlacementFormModalProps {
  isOpen: boolean;
  onClose: () => void;
}

interface FormData {
  // Personal Information
  courseChoice: string;
  name: string;
  aadhaarNumber: string;
  dateOfBirth: string;
  gender: string;
  contactNumber: string;
  emailAddress: string;
  panNumber: string;
  hostelDayScholar: string;
  parentGuardianName: string;
  parentContactNumber: string;
  district: string;
  state: string;
  country: string;

  // 10th Academic Qualification
  marks10th: string;
  totalMarks10th: string;
  percentage10th: string;
  yearOfPassing10th: string;
  schoolName10th: string;
  boardOfStudy10th: string;
  district10th: string;
  state10th: string;

  // 12th Academic Qualification
  marks12th: string;
  totalMarks12th: string;
  percentage12th: string;
  yearOfPassing12th: string;
  schoolName12th: string;
  boardOfStudy12th: string;
  district12th: string;
  state12th: string;

  // Graduation
  ugPg: string;
  admissionType: string;
  department: string;
  section: string;
  rollNumber: string;
  registrationNumber: string;

  // UG
  percentageUG: string;
  yearOfPassingUG: string;
  standingArrears: string;
  historyOfArrears: string;
  numberOfArrears: string;
  recentPhoto: File | null;
}

const PlacementFormModal: React.FC<PlacementFormModalProps> = ({ isOpen, onClose }) => {
  const [formData, setFormData] = useState<FormData>({
    courseChoice: '',
    name: '',
    aadhaarNumber: '',
    dateOfBirth: '',
    gender: '',
    contactNumber: '',
    emailAddress: '',
    panNumber: '',
    hostelDayScholar: '',
    parentGuardianName: '',
    parentContactNumber: '',
    district: '',
    state: '',
    country: '',
    marks10th: '',
    totalMarks10th: '',
    percentage10th: '',
    yearOfPassing10th: '',
    schoolName10th: '',
    boardOfStudy10th: '',
    district10th: '',
    state10th: '',
    marks12th: '',
    totalMarks12th: '',
    percentage12th: '',
    yearOfPassing12th: '',
    schoolName12th: '',
    boardOfStudy12th: '',
    district12th: '',
    state12th: '',
    ugPg: '',
    admissionType: '',
    department: '',
    section: '',
    rollNumber: '',
    registrationNumber: '',
    percentageUG: '',
    yearOfPassingUG: '',
    standingArrears: '',
    historyOfArrears: '',
    numberOfArrears: '',
    recentPhoto: null,
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null;
    setFormData(prev => ({
      ...prev,
      recentPhoto: file
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Handle form submission logic here
    console.log('Form submitted:', formData);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4">
      <div className="modal-scrollbar bg-white p-4 lg:p-8 rounded-2xl max-w-7xl w-full max-h-[90vh] overflow-y-auto ">
        {/* Header Section with Logo */}
        <div className="bg-custom-light-green p-6 rounded-2xl relative">
          <div className="flex items-center justify-start space-x-8 px-24">
            <div className="w-150 h-150">
              <Image
                src="/jmc_logo 2.svg"
                alt="JMC College Logo"
                width={60}
                height={60}
                className="object-contain w-16 h-16 lg:w-36 lg:h-36"
              />
            </div>
            <div className='text-center'>
              <h2 className="text-3xl font-medium text-custom-green font-ramilas">Students Career Enrolment Form(SCEF)</h2>
              <p className="text-md text-gray-600">Applicable only for 2021-2024(UG) & 2022-2024(PG)</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="absolute top-4 right-4 text-gray-500 hover:text-custom-green px-4 py-2"
            aria-label="Close"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Form Content */}
        <form onSubmit={handleSubmit} className="p-6 space-y-8">
          {/* Personal Information Section */}
          <div className="space-y-6">
            <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-6">
              <div className="lg:w-1/3">
                <h3 className="text-2xl font-ramilas text-custom-green relative inline-block">
                  Personal information
                  <span className="absolute bottom-0 left-0 w-full h-0.5 bg-custom-green"></span>
                </h3>
              </div>

              <div className="lg:w-2/3">
                <div className="flex flex-col lg:flex-row lg:items-center lg:gap-4">
                  <p className="text-sm text-gray-600 mb-2 lg:mb-0 lg:whitespace-nowrap">
                    Final year students should choose their course in any ONE of the following:
                  </p>
                  <Select
                    value={formData.courseChoice}
                    onValueChange={(value) => setFormData(prev => ({ ...prev, courseChoice: value }))}
                  >
                    <SelectTrigger className="w-full lg:w-auto flex-1 px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-custom-green focus:border-transparent">
                      <SelectValue placeholder="Choose your placement" />
                    </SelectTrigger>
                    <SelectContent className="rounded-md">
                      <SelectItem value="placement1">Placement Option 1</SelectItem>
                      <SelectItem value="placement2">Placement Option 2</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

            </div>


            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-custom-green mb-2">
                  Name:(As recorded in class 12th TC)
                </label>
                <input
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  placeholder="Your Name"
                  className="w-full px-4 py-2 text-sm border border-gray-300 rounded-md focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-custom-green mb-2">
                  Aadhaar Number :
                </label>
                <input
                  type="text"
                  name="aadhaarNumber"
                  value={formData.aadhaarNumber}
                  onChange={handleInputChange}
                  placeholder="Your Aadhaar No"
                  className="w-full px-4 py-2 text-sm border border-gray-300 rounded-md  focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-custom-green mb-2">
                  Date of Birth:
                </label>
                <input
                  type="date"
                  name="dateOfBirth"
                  value={formData.dateOfBirth}
                  onChange={handleInputChange}
                  className="w-full px-4 py-2 text-sm border border-gray-300 rounded-md  focus:border-transparent"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-custom-green mb-2 lg:mb-4">Gender</label>
                <div className="flex gap-4 mt-2">
                  <label className="inline-flex items-center cursor-pointer">
                    <input
                      type="radio"
                      name="gender"
                      value="Male"
                      checked={formData.gender === 'Male'}
                      onChange={handleInputChange}
                      className="sr-only"
                    />
                    <div className={`w-4 h-4 rounded-full border-2 flex items-center justify-center mr-2
    ${formData.gender === 'Male' ? 'border-custom-green' : 'border-gray-300'}`}>
                      {formData.gender === 'Male' && (
                        <div className="w-2 h-2 bg-custom-green rounded-full"></div>
                      )}
                    </div>
                    <span className={formData.gender === 'Male' ? 'text-custom-green text-sm' : 'text-gray-700 text-sm'}>
                      Male
                    </span>
                  </label>

                  <label className="flex items-center cursor-pointer">
                    <input
                      type="radio"
                      name="gender"
                      value="Female"
                      checked={formData.gender === 'Female'}
                      onChange={handleInputChange}
                      className="sr-only"
                    />
                    <div className={`w-4 h-4 rounded-full border-2 flex items-center justify-center mr-2
    ${formData.gender === 'Female' ? 'border-custom-green' : 'border-gray-300'}`}>
                      {formData.gender === 'Female' && (
                        <div className="w-2 h-2 bg-custom-green rounded-full"></div>
                      )}
                    </div>
                    <span className={formData.gender === 'Female' ? 'text-custom-green text-sm' : 'text-gray-700 text-sm'}>
                      Female
                    </span>
                  </label>

                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-custom-green mb-2 lg:mb-4">
                  Contact Number:
                </label>
                <input
                  type="tel"
                  name="contactNumber"
                  value={formData.contactNumber}
                  onChange={handleInputChange}
                  placeholder="Your Contact Number"
                  className="w-full px-4 py-2 text-sm border border-gray-300 rounded-md  focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-custom-green mb-2 lg:mb-4">
                  Email Address :
                </label>
                <input
                  type="email"
                  name="emailAddress"
                  value={formData.emailAddress}
                  onChange={handleInputChange}
                  placeholder="Your Email"
                  className="w-full px-4 py-2 text-sm border border-gray-300 rounded-md  focus:border-transparent"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-custom-green mb-2 lg:mb-4">
                  Pan Number(Optional) :
                </label>
                <input
                  type="text"
                  name="panNumber"
                  value={formData.panNumber}
                  onChange={handleInputChange}
                  placeholder="Your PAN Card No"
                  className="w-full px-4 py-2 text-sm border border-gray-300 rounded-md  focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-custom-green mb-2 lg:mb-4">
                  Hostel / Day Scholar :
                </label>
                <input
                  type="text"
                  name="hostelDayScholar"
                  value={formData.hostelDayScholar}
                  onChange={handleInputChange}
                  placeholder="Your accommodation type"
                  className="w-full px-4 py-2 text-sm border border-gray-300 rounded-md  focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-custom-green mb-2 lg:mb-4">
                  Parent / Guardian Name :
                </label>
                <input
                  type="text"
                  name="parentGuardianName"
                  value={formData.parentGuardianName}
                  onChange={handleInputChange}
                  placeholder="Enter parent or guardian's full name"
                  className="w-full px-4 py-2 text-sm border border-gray-300 rounded-md  focus:border-transparent"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-custom-green mb-2 lg:mb-4">
                  Contact Number: (Parent / Guardian)
                </label>
                <input
                  type="tel"
                  name="parentContactNumber"
                  value={formData.parentContactNumber}
                  onChange={handleInputChange}
                  placeholder="Enter Number"
                  className="w-full px-4 py-2 text-sm border border-gray-300 rounded-md  focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-custom-green mb-2 lg:mb-4">
                  District :
                </label>
                <input
                  type="text"
                  name="district"
                  value={formData.district}
                  onChange={handleInputChange}
                  placeholder="Your District"
                  className="w-full px-4 py-2 text-sm border border-gray-300 rounded-md  focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-custom-green mb-2 lg:mb-4">
                  State :
                </label>
                <input
                  type="text"
                  name="state"
                  value={formData.state}
                  onChange={handleInputChange}
                  placeholder="your State"
                  className="w-full px-4 py-2 text-sm border border-gray-300 rounded-md  focus:border-transparent"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-custom-green mb-2 lg:mb-4">
                  Country :
                </label>
                <input
                  type="text"
                  name="country"
                  value={formData.country}
                  onChange={handleInputChange}
                  placeholder="Your Country"
                  className="w-full px-4 py-2 text-sm border border-gray-300 rounded-md  focus:border-transparent"
                />
              </div>
            </div>
          </div>

          {/* Academic Qualification 10th Section */}
          <div className="space-y-6">
            <h3 className="text-2xl font-ramilas text-custom-green relative inline-block">
              Academic Qualification 10th
              <span className="absolute bottom-0 left-0 w-full h-0.5 bg-custom-green"></span>
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-custom-green mb-2 lg:mb-4">
                  Marks Obtained / Scored :
                </label>
                <input
                  type="text"
                  name="marks10th"
                  value={formData.marks10th}
                  onChange={handleInputChange}
                  placeholder="Your Obtained / Scored"
                  className="w-full px-4 py-2 text-sm border border-gray-300 rounded-md  focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-custom-green mb-2 lg:mb-4">
                  Total Marks :
                </label>
                <input
                  type="text"
                  name="totalMarks10th"
                  value={formData.totalMarks10th}
                  onChange={handleInputChange}
                  placeholder="Your Total Mark"
                  className="w-full px-4 py-2 text-sm border border-gray-300 rounded-md  focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-custom-green mb-2 lg:mb-4">
                  Percentage :
                </label>
                <input
                  type="text"
                  name="percentage10th"
                  value={formData.percentage10th}
                  onChange={handleInputChange}
                  placeholder="Your percentage"
                  className="w-full px-4 py-2 text-sm border border-gray-300 rounded-md  focus:border-transparent"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-custom-green mb-2 lg:mb-4">
                  Year of passing :
                </label>
                <input
                  type="text"
                  name="yearOfPassing10th"
                  value={formData.yearOfPassing10th}
                  onChange={handleInputChange}
                  placeholder="Your Year Of passing"
                  className="w-full px-4 py-2 text-sm border border-gray-300 rounded-md  focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-custom-green mb-2 lg:mb-4">
                  School Name(Do not use Special Characters):
                </label>
                <input
                  type="text"
                  name="schoolName10th"
                  value={formData.schoolName10th}
                  onChange={handleInputChange}
                  placeholder="Your School name"
                  className="w-full px-4 py-2 text-sm border border-gray-300 rounded-md  focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-custom-green mb-2 lg:mb-4">
                  Board of study (State / Matric / CBSE) :
                </label>
                <input
                  type="text"
                  name="boardOfStudy10th"
                  value={formData.boardOfStudy10th}
                  onChange={handleInputChange}
                  placeholder="Your Board of Study"
                  className="w-full px-4 py-2 text-sm border border-gray-300 rounded-md  focus:border-transparent"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-custom-green mb-2 lg:mb-4">
                  District :
                </label>
                <input
                  type="text"
                  name="district10th"
                  value={formData.district10th}
                  onChange={handleInputChange}
                  placeholder="Your District"
                  className="w-full px-4 py-2 text-sm border border-gray-300 rounded-md  focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-custom-green mb-2 lg:mb-4">
                  State :
                </label>
                <input
                  type="text"
                  name="state10th"
                  value={formData.state10th}
                  onChange={handleInputChange}
                  placeholder="Your State"
                  className="w-full px-4 py-2 text-sm border border-gray-300 rounded-md  focus:border-transparent"
                />
              </div>
            </div>
          </div>

          {/* Academic Qualification 12th Section */}
          <div className="space-y-6">
            <h3 className="text-2xl font-ramilas text-custom-green relative inline-block">
              Academic Qualification 12th
              <span className="absolute bottom-0 left-0 w-full h-0.5 bg-custom-green"></span>
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-custom-green mb-2 lg:mb-4">
                  Marks Obtained / Scored :
                </label>
                <input
                  type="text"
                  name="marks12th"
                  value={formData.marks12th}
                  onChange={handleInputChange}
                  placeholder="Your Obtained / Scored"
                  className="w-full px-4 py-2 text-sm border border-gray-300 rounded-md  focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-custom-green mb-2 lg:mb-4">
                  Total Marks :
                </label>
                <input
                  type="text"
                  name="totalMarks12th"
                  value={formData.totalMarks12th}
                  onChange={handleInputChange}
                  placeholder="Your Total Mark"
                  className="w-full px-4 py-2 text-sm border border-gray-300 rounded-md  focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-custom-green mb-2 lg:mb-4">
                  Percentage :
                </label>
                <input
                  type="text"
                  name="percentage12th"
                  value={formData.percentage12th}
                  onChange={handleInputChange}
                  placeholder="Your percentage"
                  className="w-full px-4 py-2 text-sm border border-gray-300 rounded-md  focus:border-transparent"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-custom-green mb-2 lg:mb-4">
                  Year of passing :
                </label>
                <input
                  type="text"
                  name="yearOfPassing12th"
                  value={formData.yearOfPassing12th}
                  onChange={handleInputChange}
                  placeholder="Your Year Of passing"
                  className="w-full px-4 py-2 text-sm border border-gray-300 rounded-md  focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-custom-green mb-2 lg:mb-4">
                  School Name(Do not use Special Characters):
                </label>
                <input
                  type="text"
                  name="schoolName12th"
                  value={formData.schoolName12th}
                  onChange={handleInputChange}
                  placeholder="Your School name"
                  className="w-full px-4 py-2 text-sm border border-gray-300 rounded-md  focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-custom-green mb-2 lg:mb-4">
                  Board of study (State / Matric / CBSE) :
                </label>
                <input
                  type="text"
                  name="boardOfStudy12th"
                  value={formData.boardOfStudy12th}
                  onChange={handleInputChange}
                  placeholder="Your Board of Study"
                  className="w-full px-4 py-2 text-sm border border-gray-300 rounded-md  focus:border-transparent"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-custom-green mb-2 lg:mb-4">
                  District :
                </label>
                <input
                  type="text"
                  name="district12th"
                  value={formData.district12th}
                  onChange={handleInputChange}
                  placeholder="Your District"
                  className="w-full px-4 py-2 text-sm border border-gray-300 rounded-md  focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-custom-green mb-2 lg:mb-4">
                  State :
                </label>
                <input
                  type="text"
                  name="state12th"
                  value={formData.state12th}
                  onChange={handleInputChange}
                  placeholder="Your State"
                  className="w-full px-4 py-2 text-sm border border-gray-300 rounded-md  focus:border-transparent"
                />
              </div>
            </div>
          </div>

          {/* Graduation Section */}
          <div className="space-y-6">
            <h3 className="text-2xl font-ramilas text-custom-green relative inline-block">
              Graduation
              <span className="absolute bottom-0 left-0 w-full h-0.5 bg-custom-green"></span>
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-custom-green mb-2 lg:mb-4">
                  UG/PG :
                </label>
                <Select
                  value={formData.ugPg}
                  onValueChange={(value) => setFormData(prev => ({ ...prev, ugPg: value }))}
                >
                  <SelectTrigger className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-custom-green focus:border-transparent">
                    <SelectValue placeholder="UG" />
                  </SelectTrigger>
                  <SelectContent className="rounded-md">
                    <SelectItem value="UG">UG</SelectItem>
                    <SelectItem value="PG">PG</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="block text-sm font-medium text-custom-green mb-2 lg:mb-4">
                  Aided / Self Finance Men /Self Finance Women :
                </label>
                <Select
                  value={formData.admissionType}
                  onValueChange={(value) => setFormData(prev => ({ ...prev, admissionType: value }))}
                >
                  <SelectTrigger className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-custom-green focus:border-transparent">
                    <SelectValue placeholder="Your admission type" />
                  </SelectTrigger>
                  <SelectContent className="rounded-md">
                    <SelectItem value="Aided">Aided</SelectItem>
                    <SelectItem value="Self Finance Men">Self Finance Men</SelectItem>
                    <SelectItem value="Self Finance Women">Self Finance Women</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="block text-sm font-medium text-custom-green mb-2 lg:mb-4">
                  Department :
                </label>
                <input
                  type="text"
                  name="department"
                  value={formData.department}
                  onChange={handleInputChange}
                  placeholder="Your Department"
                  className="w-full px-4 py-2 text-sm border border-gray-300 rounded-md  focus:border-transparent"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-custom-green mb-2 lg:mb-4">
                  Section (A/B/C) :
                </label>
                <input
                  type="text"
                  name="section"
                  value={formData.section}
                  onChange={handleInputChange}
                  placeholder="Your section"
                  className="w-full px-4 py-2 text-sm border border-gray-300 rounded-md  focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-custom-green mb-2 lg:mb-4">
                  Roll Number :
                </label>
                <input
                  type="text"
                  name="rollNumber"
                  value={formData.rollNumber}
                  onChange={handleInputChange}
                  placeholder="Your Roll Number"
                  className="w-full px-4 py-2 text-sm border border-gray-300 rounded-md  focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-custom-green mb-2 lg:mb-4">
                  Registration Number :
                </label>
                <input
                  type="text"
                  name="registrationNumber"
                  value={formData.registrationNumber}
                  onChange={handleInputChange}
                  placeholder="Your Registration"
                  className="w-full px-4 py-2 text-sm border border-gray-300 rounded-md  focus:border-transparent"
                />
              </div>
            </div>
          </div>

          {/* UG Section */}
          <div className="space-y-6">
            <h3 className="text-2xl font-ramilas text-custom-green relative inline-block">
              UG
              <span className="absolute bottom-0 left-0 w-full h-0.5 bg-custom-green"></span>
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-custom-green mb-2 lg:mb-4">
                  Percentage :
                </label>
                <Select
                  value={formData.percentageUG}
                  onValueChange={(value) => setFormData(prev => ({ ...prev, percentageUG: value }))}
                >
                  <SelectTrigger className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-custom-green focus:border-transparent">
                    <SelectValue placeholder="UG" />
                  </SelectTrigger>
                  <SelectContent className="rounded-md">
                    <SelectItem value="60-70">60-70%</SelectItem>
                    <SelectItem value="70-80">70-80%</SelectItem>
                    <SelectItem value="80-90">80-90%</SelectItem>
                    <SelectItem value="90-100">90-100%</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="block text-sm font-medium text-custom-green mb-2 lg:mb-4">
                  Year of Passing :
                </label>
                <Select
                  value={formData.yearOfPassingUG}
                  onValueChange={(value) => setFormData(prev => ({ ...prev, yearOfPassingUG: value }))}
                >
                  <SelectTrigger className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-custom-green focus:border-transparent">
                    <SelectValue placeholder="Your Admission type" />
                  </SelectTrigger>
                  <SelectContent className="rounded-md">
                    <SelectItem value="2024">2024</SelectItem>
                    <SelectItem value="2025">2025</SelectItem>
                    <SelectItem value="2026">2026</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="block text-sm font-medium text-custom-green mb-2 lg:mb-4">
                  Standing arrears
                </label>
                <div className="flex gap-4 mt-2">
                  <label className="flex items-center cursor-pointer mr-4">
                    <input
                      type="radio"
                      name="standingArrears"
                      value="Male"
                      checked={formData.standingArrears === 'Male'}
                      onChange={handleInputChange}
                      className="sr-only"
                    />
                    <div className={`w-4 h-4 rounded-full border-2 flex items-center justify-center mr-2
    ${formData.standingArrears === 'Male' ? 'border-custom-green' : 'border-gray-300'}`}>
                      {formData.standingArrears === 'Male' && (
                        <div className="w-2 h-2 bg-custom-green rounded-full"></div>
                      )}
                    </div>
                    <span className={formData.standingArrears === 'Male' ? 'text-custom-green text-sm' : 'text-gray-700 text-sm'}>
                      Male
                    </span>
                  </label>

                  <label className="flex items-center cursor-pointer">
                    <input
                      type="radio"
                      name="standingArrears"
                      value="Female"
                      checked={formData.standingArrears === 'Female'}
                      onChange={handleInputChange}
                      className="sr-only"
                    />
                    <div className={`w-4 h-4 rounded-full border-2 flex items-center justify-center mr-2
    ${formData.standingArrears === 'Female' ? 'border-custom-green' : 'border-gray-300'}`}>
                      {formData.standingArrears === 'Female' && (
                        <div className="w-2 h-2 bg-custom-green rounded-full"></div>
                      )}
                    </div>
                    <span className={formData.standingArrears === 'Female' ? 'text-custom-green text-sm' : 'text-gray-700 text-sm'}>
                      Female
                    </span>
                  </label>

                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-custom-green mb-2 lg:mb-4">
                  History of arrears
                </label>
                <div className="flex gap-4 mt-2">
                  <label className="flex items-center cursor-pointer mr-4">
                    <input
                      type="radio"
                      name="historyOfArrears"
                      value="Male"
                      checked={formData.historyOfArrears === 'Male'}
                      onChange={handleInputChange}
                      className="sr-only"
                    />
                    <div className={`w-4 h-4 rounded-full border-2 flex items-center justify-center mr-2
    ${formData.historyOfArrears === 'Male' ? 'border-custom-green' : 'border-gray-300'}`}>
                      {formData.historyOfArrears === 'Male' && (
                        <div className="w-2 h-2 bg-custom-green rounded-full"></div>
                      )}
                    </div>
                    <span className={formData.historyOfArrears === 'Male' ? 'text-custom-green text-sm' : 'text-gray-700 text-sm'}>
                      Male
                    </span>
                  </label>

                  <label className="flex items-center cursor-pointer">
                    <input
                      type="radio"
                      name="historyOfArrears"
                      value="Female"
                      checked={formData.historyOfArrears === 'Female'}
                      onChange={handleInputChange}
                      className="sr-only"
                    />
                    <div className={`w-4 h-4 rounded-full border-2 flex items-center justify-center mr-2
    ${formData.historyOfArrears === 'Female' ? 'border-custom-green' : 'border-gray-300'}`}>
                      {formData.historyOfArrears === 'Female' && (
                        <div className="w-2 h-2 bg-custom-green rounded-full"></div>
                      )}
                    </div>
                    <span className={formData.historyOfArrears === 'Female' ? 'text-custom-green text-sm' : 'text-gray-700 text-sm'}>
                      Female
                    </span>
                  </label>

                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-custom-green mb-2 lg:mb-4">
                  If yes number of arrears at present :
                </label>
                <input
                  type="text"
                  name="numberOfArrears"
                  value={formData.numberOfArrears}
                  onChange={handleInputChange}
                  placeholder="Your first Number"
                  className="w-full p-4 text-sm border border-gray-300 rounded-md  focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-custom-green mb-2 lg:mb-4">
                  Upload Your Recent Photo
                </label>
                <div className="px-2 border border-gray-300 rounded-md">
                  <div className="flex gap-2 py-3">
                    <input
                      type="file"
                      accept="image/*"
                      onChange={handleFileChange}
                      className="hidden"
                      id="photo-upload"
                    />
                    <label
                      htmlFor="photo-upload"
                      className="bg-custom-green text-md text-white px-4 rounded-md cursor-pointer hover:bg-green-700 transition-colors"
                    >
                      Upload file
                    </label>
                    <span className="text-sm text-gray-500 self-center">
                      {formData.recentPhoto ? formData.recentPhoto.name : 'No file chosen'}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* Submit Button */}
            <div className="flex justify-end pt-6">
              <button
                type="submit"
                className="bg-custom-green text-white px-32 py-2 rounded-full font-medium hover:bg-green-700 transition-colors duration-300 text-lg"
              >
                Submit now
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
};

export default PlacementFormModal;