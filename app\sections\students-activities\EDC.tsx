"use client";

import { Profile, ProfileCard } from "@/app/components/ProfileCard";
import Image from "next/image";

const EDC = () => {
  const activities: any[] = [
    {
      title: "Discipline Specific Programmes - EDC (2023 -2024)",
      link: "https://jmc.edu/include/edc/pdf/Discipline-Specific-Programmes-EDC(2023-2024).pdf",
    },
    {
      title: "Entrepreneurship Development Cell - Activities 2023-2024",
      link: "https://jmc.edu/include/edc/pdf/EDC-REPORT-2023-2024.pdf",
    },
    {
      title: "Entrepreneurship Development Cell - Activities 2024-2025",
      link: "https://jmc.edu/include/edc/pdf/EDC-REPORT-2024-2025.pdf",
    },
  ];
  const profiles: Profile[] = [
    {
      name: "Dr.<PERSON><PERSON>",
      role: "Coordinator",
      image: "https://jmc.edu/include/studenthealth/img/ZAHIR.png",
      qualification: "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>(Edn)., <PERSON><PERSON><PERSON>, PGDGC.,",
    },
    {
      name: "Dr. <PERSON><PERSON>",
      role: "Member",
      image: "https://jmc.edu/include/studenthealth/img/sebastinraj.png",
      qualification: "M.Sc., Ph.D.,",
    },
  ];
  return (
    <section className="bg-white pt-10 space-y-8">
      {/* about EDC */}
      <div className="container max-w-6xl mx-auto space-y-8 px-4">
        <h2 className="text-custom-green text-center">
          Entrepreneurship Development Cell (EDC)
        </h2>
        <div className="max-w-5xl mx-auto flex flex-col md:flex-row justify-center items-center gap-12 mb-6">
          <div className="w-full md:w-1/2">
            <Image
              src="/edc1.webp"
              alt="EDC Poster 1"
              width={800}
              height={800}
              className="w-full h-auto rounded-lg shadow-md"
            />
          </div>
          <div className="w-full md:w-1/2">
            <Image
              src="/edc2.webp"
              alt="EDC Poster 2"
              width={800}
              height={800}
              className="w-full h-auto rounded-lg shadow-md"
            />
          </div>
        </div>
        <p className="text-center text-custom-new-green mb-10">
          The motto of Entrepreneurship Development Cell (EDC) in our college is
          to develop novel ideas and to provide infrastructure, amenities and
          technical support to the students for the betterment of the society.
        </p>
        <article
          aria-labelledby="vision-objectives"
          className="grid grid-cols-1 md:grid-cols-2 gap-6"
        >
          <div className="bg-white shadow-custom rounded-lg p-6">
            <h3 id="vision-objectives" className="mb-4 text-custom-green">
              Vision
            </h3>
            <ul className="list-disc pl-8 text-custom-new-green font-poppins">
              <li>
                To create an awareness on entrepreneurship among the students
              </li>
              <li>
                To develop the advanced skill of creativity and innovative
                thinking
              </li>
              <li>
                To provide all support like planning and mentoring for the
                startup ideas to convert them into real business adventures
              </li>
            </ul>
          </div>

          <div className="bg-white shadow-custom rounded-lg p-6">
            <h3 className="mb-4 text-custom-green">Objective</h3>
            <ul className="list-disc pl-8 text-custom-new-green font-poppins">
              <li>
                To empower and inspire students to take initiatives and accept
                responsibilities to shine well in the challenging world
              </li>
              <li>To become job creators rather than seekers</li>
              <li>
                To foster better linkages between the Parent Institution,
                Industries, R&D institutions and SME promotion bodies
              </li>
              <li>To catalyze and promote employment opportunities</li>
            </ul>
          </div>
        </article>
      </div>
      {/* Members of EDC */}
      <section className="bg-[#F7F8F7] py-10 px-4">
        <div className="container max-w-7xl mx-auto space-y-6">
          <h2 className="text-custom-green text-center">Members of EDC</h2>
          <div className="grid gap-4">
            {profiles.length > 0 ? (
              profiles.map((profile, index) =>
                profile && typeof profile === "object" ? (
                  <ProfileCard
                    key={index}
                    name={(profile as any).name}
                    role={(profile as any).role}
                    image={(profile as any).image}
                    description={(profile as any).description}
                    qualification={(profile as any).qualification}
                    swapHeaderOrder={true}
                  />
                ) : null
              )
            ) : (
              <p className="text-gray-500">No profiles available.</p>
            )}
          </div>
        </div>
      </section>

      {/* Functions */}
      <section className="bg-white pb-10 px-4">
        <div className="container max-w-7xl mx-auto space-y-6">
          <h2 className="text-custom-green">Functions</h2>
          <div className="rounded-2xl border border-gray-200 p-4 md:p-10 lg:px-16 lg:py-10 shadow-sm bg-white space-y-6">
            <ul className="list-disc list-outside pl-8 space-y-2 text-custom-new-green font-poppins">
              <li>
                EDC – JMC has framed a panel of faculty members as the
                facilitators for directing students as the active members. The
                members of EDC – JMC will be selected on the basis of their
                interest and involvement to work voluntarily
              </li>
              <li>
                To select a student as the mentor for a group of students who
                have business ideas under the supervision of entrepreneurs and
                the team of EDC faculty advisors
              </li>
            </ul>
            <div className="space-y-4">
              <h3 className="text-custom-green text-fluid-base font-poppins font-semibold">
                Training and Coaching Programmes
              </h3>
              <ul className="list-disc list-outside pl-8 space-y-2 text-custom-new-green font-poppins">
                <li>
                  To guide them how to present ideas, documentations, marketing
                  skills and finance management
                </li>
                <li>
                  To provide a platform for interaction with established
                  entrepreneurs "face to face" programmes
                </li>
              </ul>
            </div>
            <div className="space-y-4">
              <h3 className="text-custom-green text-fluid-base font-poppins font-semibold">
                Activities
              </h3>
              <ul className="list-disc list-outside pl-8 space-y-2 text-custom-new-green font-poppins">
                <li>
                  To arrange for visits to industries for prospective
                  entrepreneurs
                </li>
                <li>
                  To organize entrepreneurship awareness camps, entrepreneurship
                  development programmes, skill development programmes
                </li>
                <li>
                  To arrange guest lectures by successful entrepreneurs and
                  provide a platform for interaction between professional
                  entrepreneurs and student entrepreneurs
                </li>
                <li>
                  To provide provisions to initiate innovative student projects
                </li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Activities */}
      <section className="bg-white pb-10 px-4">
        <div className="container max-w-7xl mx-auto space-y-6">
          <h2 className="text-custom-green">Activities</h2>
          <div className="rounded-2xl border border-gray-200 p-4 md:p-10 shadow-sm bg-white">
            <ul className="list-disc pl-4 md:pl-6 list-outside mt-2 space-y-1 font-poppins font-semibold custom-marker">
              {activities.map((activity, idx) => (
                <li key={idx}>
                  <a
                    href={activity.link}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-custom-new-green underline underline-offset-2 hover:text-green-800"
                  >
                    {activity.title}
                  </a>
                </li>
              ))}
            </ul>
          </div>
        </div>
      </section>
    </section>
  );
};

export default EDC;
