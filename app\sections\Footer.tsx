"use client";

import Image from "next/image";
import Link from "next/link";

export default function Footer() {
  return (
    // Removed px-10 md:px-24 from footer, kept py-8
    <footer className="bg-custom-green text-white ">
      {/* Container for main footer content - Added px-10 md:px-24 */}
      <div className="container mx-auto max-w-7xl px-10 md:px-12 py-8">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-12 gap-x-16 gap-y-12">
          {/* Logo and Description Section - Updated Layout */}
          <div className="lg:col-span-4 flex items-start gap-4"> 
            {/* Logo on the left - Adjusted responsive size */}
            <Image
              src="/jmc_logo 2.svg"
              alt="JMC College Logo"
              width={120} // Updated base width for optimization hint
              height={120} // Updated base height for optimization hint
              className="flex-shrink-0 mt-1 w-[120px] h-[120px] md:w-[150px] md:h-[150px]" // Larger base, even larger for lg
            />
            {/* Right side content container */}
            <div className="space-y-3"> 
              <h3 className="text-xl font-ramilas font-bold"> 
                JMC College
              </h3>
              <p className="text-sm leading-6"> 
                We are passionate education dedicated to providing high-quality
                resources learners all backgrounds.
              </p>
              {/* Social Icons */}
              <div className="flex space-x-3 pt-1"> 
                <Link 
                  href="https://www.facebook.com/share/1AdCviZ1VW/" 
                  className="hover:opacity-80 transition-opacity"
                  target="_blank" 
                  rel="noopener noreferrer"
                  aria-label="Facebook"
                >
                  <Image
                    src="/facebook.svg"
                    alt="Facebook Icon"
                    width={24} 
                    height={24}
                  />
                </Link>
                <Link 
                  href="https://www.instagram.com/jamal_mohamed_college_trichy/profilecard/?igsh=aWFqb2hpYm5xaHpz" 
                  className="hover:opacity-80 transition-opacity"
                  target="_blank" 
                  rel="noopener noreferrer"
                  aria-label="Instagram"
                >
                  <Image
                    src="/instagram.svg"
                    alt="Instagram Icon"
                    width={24}
                    height={24}
                  />
                </Link>
                <Link 
                  href="https://www.linkedin.com/company/jamal-mohamed-college-autonomous/" 
                  className="hover:opacity-80 transition-opacity"
                  target="_blank" 
                  rel="noopener noreferrer"
                  aria-label="LinkedIn"
                >
                  <Image
                    src="/linkedin.svg"
                    alt="LinkedIn Icon"
                    width={24}
                    height={24}
                  />
                </Link>
                <Link 
                  href="https://www.youtube.com/channel/UCdpNwtJ34rm7GAvm_4ajeHg/videos" 
                  className="hover:opacity-80 transition-opacity"
                  target="_blank" 
                  rel="noopener noreferrer"
                  aria-label="YouTube"
                >
                  <Image
                    src="/youtube.svg"
                    alt="YouTube Icon"
                    width={24}
                    height={24}
                  />
                </Link>
              </div>
            </div>
          </div>

          {/* Navigation Links Section */}
          <div className="lg:col-span-8 grid grid-cols-2 md:grid-cols-3 gap-x-8 gap-y-8"> 
            {/* College Related Links */}
            <div>
              <h3 className="text-lg font-ramilas font-bold mb-4">College Related Links</h3>
              <ul className="text-sm space-y-2">
                <li><Link href="#" className="hover:text-gray-300">BDU</Link></li>
                <li><Link href="#" className="hover:text-gray-300">UGC</Link></li>
                <li><Link href="#" className="hover:text-gray-300">AICTE</Link></li>
                <li><Link href="#" className="hover:text-gray-300">NAAC</Link></li>
              </ul>
            </div>
            {/* Research Related Links */}
            <div>
              <h3 className="text-lg font-ramilas font-bold mb-4">Research Related Links</h3>
              <ul className="text-sm space-y-2">
                <li><Link href="#" className="hover:text-gray-300">Research Programmes</Link></li>
                <li><Link href="#" className="hover:text-gray-300">Research Portal</Link></li>
                <li><Link href="#" className="hover:text-gray-300">UGC Care</Link></li>
                <li><Link href="#" className="hover:text-gray-300">Scopus</Link></li>
              </ul>
            </div>
            {/* Student Related Links */}
            <div>
              <h3 className="text-lg font-ramilas font-bold mb-4">Student Related Links</h3>
              <ul className="text-sm space-y-2">
                <li><Link href="/admission" className="hover:text-gray-300">Admission</Link></li>
                <li><Link href="#" className="hover:text-gray-300">Placement</Link></li>
                <li><Link href="#" className="hover:text-gray-300">Hostel</Link></li>
                <li><Link href="#" className="hover:text-gray-300">Scholarships</Link></li>
                <li><Link href="#" className="hover:text-gray-300">E-Portal</Link></li>
                <li><Link href="#" className="hover:text-gray-300">Digital Library</Link></li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      {/* Full Width Separator - Outside the main content container */}
      <div className="border-t border-white/20"></div>

      {/* Copyright Container - Adjusted text size and alignment */}
      <div className="container mx-auto max-w-7xl px-10 md:px-24 pt-8 pb-4 text-center md:text-left text-[11px] md:text-xs text-gray-400"> 
        <p>
          Copyright © 2025. All Rights Reserved by Jamal Mohamed College
        </p>
      </div>
    </footer>
  );
}
